server:
  port: 7003
#  port: 7001 # https默认访问端口
  #port: 19001 #Thread
  #port: 18051
  #port: 16001  #token
  #port:  20001  #test


#  ssl:
#    key-store: classpath:server.keystore # 证书存放的位置
#    key-alias: tomcat # 证书别名
#    key-store-type: JKS # P12证书格式
#    key-store-password: 123456
#    enabled-protocols: TLSv1.2

#  port: 8091

spring:
    profiles:
        active: dev
    datasource:
        url: jdbc:dm://10.1.1.17:5236
        driver-class-name: dm.jdbc.driver.DmDriver
        username: SYSDBA
        password: SYSDBA
app:
  task:
    enabled: true # 开关定时任务

mybatis:
  # 指定sql映射文件位置
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.wzt.entity
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

